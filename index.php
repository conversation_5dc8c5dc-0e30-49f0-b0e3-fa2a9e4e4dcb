<?php
/**
 * JobSpace - Root Index File
 * Redirects to public directory or handles routing
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Get the request URI
$request = $_SERVER['REQUEST_URI'] ?? '/';
$path = parse_url($request, PHP_URL_PATH);

// Remove jobspace from path if present
$path = str_replace('/jobspace', '', $path);
$path = trim($path, '/');

// If accessing root, show home page
if (empty($path) || $path === 'index.php') {
    // Include home page directly
    include 'resources/views/pages/home.php';
    exit;
}

// Simple routing for other pages
switch ($path) {
    case 'home':
        include 'resources/views/pages/home.php';
        break;
        
    case 'about':
        if (file_exists('resources/views/pages/about.php')) {
            include 'resources/views/pages/about.php';
        } else {
            echo '<h1>About Page - Coming Soon</h1>';
        }
        break;
        
    case 'contact':
        if (file_exists('resources/views/pages/contact.php')) {
            include 'resources/views/pages/contact.php';
        } else {
            echo '<h1>Contact Page - Coming Soon</h1>';
        }
        break;
        
    case 'auth/login':
        if (file_exists('resources/views/auth/login.php')) {
            include 'resources/views/auth/login.php';
        } else {
            echo '<h1>Login Page - Coming Soon</h1>';
        }
        break;
        
    case 'auth/register':
        if (file_exists('resources/views/auth/register.php')) {
            include 'resources/views/auth/register.php';
        } else {
            echo '<h1>Register Page - Coming Soon</h1>';
        }
        break;
        
    case 'test':
        echo '<h1>Test Page</h1>';
        echo '<p>PHP is working!</p>';
        echo '<p>Current time: ' . date('Y-m-d H:i:s') . '</p>';
        echo '<p>Request URI: ' . ($_SERVER['REQUEST_URI'] ?? 'Not set') . '</p>';
        echo '<p>Path: ' . $path . '</p>';
        break;
        
    default:
        // 404 page
        http_response_code(404);
        if (file_exists('resources/views/errors/404.php')) {
            include 'resources/views/errors/404.php';
        } else {
            echo '<h1>404 - Page Not Found</h1>';
            echo '<p>The page you are looking for does not exist.</p>';
            echo '<a href="/">Go to Home</a>';
        }
        break;
}
?>
