<?php
echo "<h1>JobSpace Debug Information</h1>";

echo "<h2>Server Information:</h2>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>Server:</strong> " . $_SERVER['HTTP_HOST'] . "</p>";
echo "<p><strong>Request URI:</strong> " . ($_SERVER['REQUEST_URI'] ?? 'Not set') . "</p>";
echo "<p><strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p><strong>Current Directory:</strong> " . __DIR__ . "</p>";

echo "<h2>File Structure Check:</h2>";

$files_to_check = [
    '../resources/views/pages/home.php',
    '../resources/views/layouts/public-layout.php',
    '../resources/views/includes/header/public-header.php',
    '../resources/views/includes/footer/public-footer.php',
    '../resources/views/components/home/<USER>'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✓ $file exists</p>";
    } else {
        echo "<p style='color: red;'>✗ $file NOT FOUND</p>";
    }
}

echo "<h2>Test Home Page Include:</h2>";
try {
    echo "<p>Attempting to include home page...</p>";
    ob_start();
    include '../resources/views/pages/home.php';
    $output = ob_get_clean();
    echo "<p style='color: green;'>✓ Home page included successfully!</p>";
    echo "<p>Output length: " . strlen($output) . " characters</p>";
    
    if (strlen($output) > 100) {
        echo "<h3>Home Page Preview (first 500 chars):</h3>";
        echo "<pre>" . htmlspecialchars(substr($output, 0, 500)) . "...</pre>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error including home page: " . $e->getMessage() . "</p>";
} catch (Error $e) {
    echo "<p style='color: red;'>✗ Fatal error including home page: " . $e->getMessage() . "</p>";
}

echo "<h2>Direct Links:</h2>";
echo "<p><a href='index.php'>Test Main Index</a></p>";
echo "<p><a href='../resources/views/pages/home.php'>Direct Home Page</a></p>";
?>
