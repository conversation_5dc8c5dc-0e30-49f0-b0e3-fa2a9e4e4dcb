<?php

namespace App\Core;

/**
 * Router Class - QuizSpace
 * Professional routing system for handling HTTP requests
 */
class Router
{
    /**
     * @var array Registered routes
     */
    private $routes = [];

    /**
     * @var array Route parameters
     */
    private $params = [];

    /**
     * Register a GET route
     */
    public function get($route, $handler)
    {
        $this->addRoute('GET', $route, $handler);
    }

    /**
     * Register a POST route
     */
    public function post($route, $handler)
    {
        $this->addRoute('POST', $route, $handler);
    }

    /**
     * Add a route to the routes array
     */
    private function addRoute($method, $route, $handler)
    {
        $this->routes[$method][$route] = $handler;
    }

    /**
     * Dispatch the current request
     */
    public function dispatch($method, $uri)
    {
        $method = strtoupper($method);

        // Check for exact match first
        if (isset($this->routes[$method][$uri])) {
            $this->handleRoute($this->routes[$method][$uri]);
            return;
        }

        // Check for parameterized routes
        foreach ($this->routes[$method] ?? [] as $route => $handler) {
            if ($this->matchRoute($route, $uri)) {
                $this->handleRoute($handler);
                return;
            }
        }

        // No route found, handle 404
        $this->handle404();
    }

    /**
     * Match a route pattern against the current URI
     */
    private function matchRoute($route, $uri)
    {
        // Convert route pattern to regex
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $route);
        $pattern = '#^' . $pattern . '$#';

        if (preg_match($pattern, $uri, $matches)) {
            // Extract parameter names from route
            preg_match_all('/\{([^}]+)\}/', $route, $paramNames);
            
            // Map parameter values to names
            $this->params = [];
            for ($i = 1; $i < count($matches); $i++) {
                if (isset($paramNames[1][$i - 1])) {
                    $this->params[$paramNames[1][$i - 1]] = $matches[$i];
                }
            }
            
            return true;
        }

        return false;
    }

    /**
     * Handle the matched route
     */
    private function handleRoute($handler)
    {
        try {
            if (is_callable($handler)) {
                // Handle closure
                call_user_func_array($handler, array_values($this->params));
            } elseif (is_string($handler)) {
                if (strpos($handler, '@') !== false) {
                    // Handle Controller@method
                    list($controller, $method) = explode('@', $handler);
                    $this->callControllerMethod($controller, $method);
                } else {
                    // Handle view file
                    $this->renderView($handler);
                }
            } else {
                throw new \Exception('Invalid route handler');
            }
        } catch (\Exception $e) {
            $this->handle500($e);
        }
    }

    /**
     * Call a controller method
     */
    private function callControllerMethod($controller, $method)
    {
        // Add namespace if not present
        if (strpos($controller, '\\') === false) {
            $controller = 'App\\Controllers\\' . $controller;
        }

        if (!class_exists($controller)) {
            throw new \Exception("Controller {$controller} not found");
        }

        $controllerInstance = new $controller();

        if (!method_exists($controllerInstance, $method)) {
            throw new \Exception("Method {$method} not found in controller {$controller}");
        }

        // Call the method with parameters
        call_user_func_array([$controllerInstance, $method], array_values($this->params));
    }

    /**
     * Render a view file
     */
    private function renderView($view)
    {
        $viewFile = VIEWS_PATH . '/' . str_replace('.', '/', $view) . '.php';
        
        if (!file_exists($viewFile)) {
            throw new \Exception("View file {$viewFile} not found");
        }

        // Extract parameters for view
        extract($this->params);
        
        // Include the view file
        include $viewFile;
    }

    /**
     * Handle 404 Not Found
     */
    private function handle404()
    {
        http_response_code(404);
        
        // Try to load custom 404 page
        $errorView = VIEWS_PATH . '/errors/404.php';
        if (file_exists($errorView)) {
            include $errorView;
        } else {
            echo $this->getDefault404Page();
        }
    }

    /**
     * Handle 500 Internal Server Error
     */
    private function handle500(\Exception $e)
    {
        http_response_code(500);
        
        // Try to load custom 500 page
        $errorView = VIEWS_PATH . '/errors/500.php';
        if (file_exists($errorView)) {
            include $errorView;
        } else {
            echo $this->getDefault500Page($e);
        }
    }

    /**
     * Get default 404 page HTML
     */
    private function getDefault404Page()
    {
        return '<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>পেজ পাওয়া যায়নি - QuizSpace</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light d-flex align-items-center min-vh-100">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 text-center">
                <div class="card shadow">
                    <div class="card-body p-5">
                        <h1 class="display-1 fw-bold text-primary">404</h1>
                        <h2 class="h4 mb-3">পেজ পাওয়া যায়নি!</h2>
                        <p class="text-muted mb-4">দুঃখিত, আপনি যে পেজটি খুঁজছেন সেটি পাওয়া যায়নি।</p>
                        <a href="/" class="btn btn-primary">হোম পেজে যান</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>';
    }

    /**
     * Get default 500 page HTML
     */
    private function getDefault500Page(\Exception $e)
    {
        return '<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>সার্ভার সমস্যা - QuizSpace</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light d-flex align-items-center min-vh-100">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 text-center">
                <div class="card shadow">
                    <div class="card-body p-5">
                        <h1 class="display-1 fw-bold text-danger">500</h1>
                        <h2 class="h4 mb-3">সার্ভার সমস্যা!</h2>
                        <p class="text-muted mb-4">দুঃখিত, আমাদের সার্ভারে একটি সমস্যা হয়েছে।</p>
                        <div class="d-flex justify-content-center gap-3">
                            <button class="btn btn-primary" onclick="location.reload()">আবার চেষ্টা করুন</button>
                            <a href="/" class="btn btn-outline-primary">হোম পেজে যান</a>
                        </div>
                        <div class="alert alert-danger text-start mt-4">
                            <h5>Error Details:</h5>
                            <p><strong>Message:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>
                            <p><strong>File:</strong> ' . htmlspecialchars($e->getFile()) . '</p>
                            <p><strong>Line:</strong> ' . $e->getLine() . '</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>';
    }

    /**
     * Get route parameters
     */
    public function getParam($key = null, $default = null)
    {
        if ($key === null) {
            return $this->params;
        }

        return $this->params[$key] ?? $default;
    }
}
