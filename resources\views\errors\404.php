<?php
/**
 * 404 Error Page - QuizSpace
 * Professional 404 error page with navigation options
 */

// SEO Meta Data
$pageData = [
    'title' => 'পেজ পাওয়া যায়নি - QuizSpace | 404 Error',
    'description' => 'দুঃখিত, আপনি যে পেজটি খুঁজছেন সেটি পাওয়া যায়নি। QuizSpace এর অন্যান্য পেজ দেখুন।',
    'keywords' => '404, page not found, error, quizspace',
    'canonical' => 'https://quizspace.com/404',
    'og_title' => 'পেজ পাওয়া যায়নি - QuizSpace',
    'og_description' => 'এই পেজটি পাওয়া যায়নি। QuizSpace এর হোম পেজে ফিরে যান।',
    'og_image' => '/public/assets/images/og-404.jpg'
];

// Start output buffering
ob_start();
?>

<!-- 404 Error Section -->
<section class="py-5 bg-light min-vh-100 d-flex align-items-center">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="error-card bg-white rounded-4 p-5 shadow-lg text-center">
                    <!-- Floating Animation -->
                    <div class="floating-icon mb-4">
                        <i class="fas fa-search text-muted" style="font-size: 4rem; animation: float 3s ease-in-out infinite;"></i>
                    </div>

                    <!-- Error Number -->
                    <div class="error-number mb-4" style="font-size: 8rem; font-weight: 900; background: linear-gradient(135deg, #667eea, #764ba2); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; line-height: 1;">
                        404
                    </div>

                    <!-- Error Message -->
                    <h1 class="h2 fw-bold text-dark mb-3">পেজ পাওয়া যায়নি!</h1>

                    <p class="lead text-muted mb-4">
                        দুঃখিত, আপনি যে পেজটি খুঁজছেন সেটি আমরা খুঁজে পাইনি।
                        হয়তো পেজটি সরানো হয়েছে বা URL টি ভুল।
                    </p>

                    <!-- Action Buttons -->
                    <div class="d-flex flex-wrap justify-content-center gap-3 mb-5">
                        <a href="/" class="btn btn-primary btn-lg">
                            <i class="fas fa-home me-2"></i>হোম পেজে যান
                        </a>
                        <a href="/quiz" class="btn btn-outline-primary btn-lg">
                            <i class="fas fa-brain me-2"></i>কুইজ খেলুন
                        </a>
                        <a href="/contact" class="btn btn-outline-success btn-lg">
                            <i class="fas fa-envelope me-2"></i>যোগাযোগ করুন
                        </a>
                    </div>

                    <!-- Popular Links -->
                    <div class="row text-center mb-4">
                        <div class="col-md-4">
                            <div class="popular-link p-3 rounded-3 bg-light mb-3">
                                <i class="fas fa-brain text-primary fa-2x mb-2"></i>
                                <h6 class="fw-bold">কুইজ খেলুন</h6>
                                <small class="text-muted">মজার কুইজে অংশ নিয়ে আয় করুন</small>
                                <div class="mt-2">
                                    <a href="/quiz" class="btn btn-sm btn-outline-primary">শুরু করুন</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="popular-link p-3 rounded-3 bg-light mb-3">
                                <i class="fas fa-coins text-warning fa-2x mb-2"></i>
                                <h6 class="fw-bold">টাকা আয় করুন</h6>
                                <small class="text-muted">প্রতিদিন আয় করার সুযোগ</small>
                                <div class="mt-2">
                                    <a href="/register" class="btn btn-sm btn-outline-warning">যোগ দিন</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="popular-link p-3 rounded-3 bg-light mb-3">
                                <i class="fas fa-users text-info fa-2x mb-2"></i>
                                <h6 class="fw-bold">কমিউনিটি</h6>
                                <small class="text-muted">বন্ধুদের সাথে যুক্ত হন</small>
                                <div class="mt-2">
                                    <a href="/community" class="btn btn-sm btn-outline-info">দেখুন</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Search Box -->
                    <div class="search-section mb-4">
                        <h5 class="fw-bold mb-3">কিছু খুঁজছেন?</h5>
                        <div class="row justify-content-center">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="এখানে সার্চ করুন..." id="searchInput">
                                    <button class="btn btn-primary" type="button" onclick="performSearch()">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Footer -->
                    <div class="border-top pt-4 mt-4">
                        <p class="text-muted mb-0">
                            <i class="fas fa-heart text-danger me-1"></i>
                            QuizSpace - আপনার বিশ্বস্ত আয়ের সাথী
                        </p>
                        <small class="text-muted">
                            সমস্যা অব্যাহত থাকলে আমাদের সাথে যোগাযোগ করুন: <EMAIL>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Styles -->
<style>
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.error-card {
    transition: all 0.3s ease;
}

.popular-link {
    transition: all 0.3s ease;
    border: 1px solid rgba(0,0,0,0.05);
}

.popular-link:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.floating-icon {
    animation: float 3s ease-in-out infinite;
}
</style>

<!-- Scripts -->
<script>
function performSearch() {
    const query = document.getElementById('searchInput').value.trim();
    if (query) {
        // Redirect to search or home with query
        window.location.href = `/?search=${encodeURIComponent(query)}`;
    }
}

// Enter key support for search
document.getElementById('searchInput').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        performSearch();
    }
});

// Auto redirect countdown (optional)
let countdown = 30;
function startCountdown() {
    const countdownElement = document.createElement('div');
    countdownElement.className = 'alert alert-info mt-3';
    countdownElement.innerHTML = `<i class="fas fa-clock me-2"></i><span id="countdown">${countdown}</span> সেকেন্ডে হোম পেজে নিয়ে যাওয়া হবে... <button class="btn btn-sm btn-outline-primary ms-2" onclick="cancelCountdown()">বাতিল</button>`;
    document.querySelector('.error-card').appendChild(countdownElement);

    const timer = setInterval(() => {
        countdown--;
        const countdownSpan = document.getElementById('countdown');
        if (countdownSpan) {
            countdownSpan.textContent = countdown;
        }

        if (countdown <= 0) {
            clearInterval(timer);
            window.location.href = '/';
        }
    }, 1000);

    window.countdownTimer = timer;
}

function cancelCountdown() {
    if (window.countdownTimer) {
        clearInterval(window.countdownTimer);
        const alertElement = document.querySelector('.alert-info');
        if (alertElement) {
            alertElement.remove();
        }
    }
}

// Start countdown after 10 seconds
setTimeout(startCountdown, 10000);
</script>

<?php
// Get content and prepare for layout
$content = ob_get_clean();
$title = $pageData['title'];
$description = $pageData['description'];
$keywords = $pageData['keywords'];
$canonical = $pageData['canonical'];
$ogData = [
    'title' => $pageData['og_title'],
    'description' => $pageData['og_description'],
    'image' => $pageData['og_image'],
    'type' => 'website',
    'url' => $pageData['canonical']
];

// Include main layout
include __DIR__ . '/../layouts/public-layout.php';
?>