// Main JavaScript for JobSpace Application

document.addEventListener('DOMContentLoaded', function() {
    initializeHeader();
    initializeSearch();
    initializeScrollEffects();
    initializeAccessibility();
});

// Header Initialization
function initializeHeader() {
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const navMenu = document.getElementById('navMenu');
    
    if (mobileMenuToggle && navMenu) {
        mobileMenuToggle.addEventListener('click', function() {
            navMenu.classList.toggle('active');
            
            // Update aria-expanded attribute
            const isExpanded = navMenu.classList.contains('active');
            mobileMenuToggle.setAttribute('aria-expanded', isExpanded);
            
            // Change icon
            const icon = mobileMenuToggle.querySelector('i');
            icon.className = isExpanded ? 'fas fa-times' : 'fas fa-bars';
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!mobileMenuToggle.contains(e.target) && !navMenu.contains(e.target)) {
                navMenu.classList.remove('active');
                mobileMenuToggle.setAttribute('aria-expanded', 'false');
                mobileMenuToggle.querySelector('i').className = 'fas fa-bars';
            }
        });
    }
}

// Search Functionality
function initializeSearch() {
    const searchInput = document.querySelector('.search-input');
    const searchContainer = document.querySelector('.search-container');
    
    if (searchInput && searchContainer) {
        let searchTimeout;
        let searchDropdown;
        
        // Create search dropdown
        searchDropdown = document.createElement('div');
        searchDropdown.className = 'search-dropdown';
        searchContainer.appendChild(searchDropdown);
        
        searchInput.addEventListener('input', function(e) {
            const query = e.target.value.trim();
            
            clearTimeout(searchTimeout);
            
            if (query.length >= 2) {
                searchInput.classList.add('loading');
                
                searchTimeout = setTimeout(() => {
                    performSearch(query, searchDropdown);
                    searchInput.classList.remove('loading');
                }, 300);
            } else {
                searchDropdown.classList.remove('active');
            }
        });
        
        // Close search dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!searchContainer.contains(e.target)) {
                searchDropdown.classList.remove('active');
            }
        });
        
        // Handle keyboard navigation
        searchInput.addEventListener('keydown', function(e) {
            const items = searchDropdown.querySelectorAll('.search-result-item');
            let currentIndex = Array.from(items).findIndex(item => item.classList.contains('highlighted'));
            
            switch(e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    currentIndex = currentIndex < items.length - 1 ? currentIndex + 1 : 0;
                    highlightSearchItem(items, currentIndex);
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    currentIndex = currentIndex > 0 ? currentIndex - 1 : items.length - 1;
                    highlightSearchItem(items, currentIndex);
                    break;
                case 'Enter':
                    e.preventDefault();
                    if (currentIndex >= 0 && items[currentIndex]) {
                        items[currentIndex].click();
                    }
                    break;
                case 'Escape':
                    searchDropdown.classList.remove('active');
                    searchInput.blur();
                    break;
            }
        });
    }
}

// Perform Search (Mock Implementation)
function performSearch(query, dropdown) {
    // This would typically make an AJAX request to your backend
    const mockResults = [
        { title: 'Software Developer', type: 'Job', company: 'Tech Corp', location: 'Dhaka' },
        { title: 'Marketing Manager', type: 'Job', company: 'Marketing Inc', location: 'Chittagong' },
        { title: 'Tech Corp', type: 'Company', employees: '500+', industry: 'Technology' },
        { title: 'Data Analyst', type: 'Job', company: 'Data Solutions', location: 'Sylhet' }
    ].filter(item => 
        item.title.toLowerCase().includes(query.toLowerCase()) ||
        (item.company && item.company.toLowerCase().includes(query.toLowerCase()))
    );
    
    displaySearchResults(mockResults, dropdown);
}

// Display Search Results
function displaySearchResults(results, dropdown) {
    dropdown.innerHTML = '';
    
    if (results.length === 0) {
        dropdown.innerHTML = '<div class="search-result-item">No results found</div>';
    } else {
        results.forEach((result, index) => {
            const item = document.createElement('div');
            item.className = 'search-result-item';
            item.innerHTML = `
                <div class="search-result-title">${result.title}</div>
                <div class="search-result-meta">
                    ${result.type === 'Job' ? 
                        `${result.company} • ${result.location}` : 
                        `${result.employees} employees • ${result.industry}`
                    }
                </div>
            `;
            
            item.addEventListener('click', function() {
                // Handle result selection
                if (result.type === 'Job') {
                    window.location.href = `/jobs/${result.title.toLowerCase().replace(/\s+/g, '-')}`;
                } else {
                    window.location.href = `/companies/${result.title.toLowerCase().replace(/\s+/g, '-')}`;
                }
            });
            
            dropdown.appendChild(item);
        });
    }
    
    dropdown.classList.add('active');
}

// Highlight Search Item
function highlightSearchItem(items, index) {
    items.forEach(item => item.classList.remove('highlighted'));
    if (items[index]) {
        items[index].classList.add('highlighted');
    }
}

// Scroll Effects
function initializeScrollEffects() {
    const header = document.querySelector('.header-main');
    let lastScrollTop = 0;
    
    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        // Add scrolled class for styling
        if (scrollTop > 50) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
        
        // Hide/show header on scroll (optional)
        if (scrollTop > lastScrollTop && scrollTop > 100) {
            // Scrolling down
            header.style.transform = 'translateY(-100%)';
        } else {
            // Scrolling up
            header.style.transform = 'translateY(0)';
        }
        
        lastScrollTop = scrollTop;
    });
}

// Accessibility Improvements
function initializeAccessibility() {
    // Add ARIA labels and roles
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
        searchInput.setAttribute('aria-label', 'Search for jobs and companies');
        searchInput.setAttribute('role', 'searchbox');
    }
    
    const themeToggle = document.querySelector('.theme-toggle');
    if (themeToggle) {
        themeToggle.setAttribute('aria-label', 'Toggle dark/light theme');
    }
    
    const langToggle = document.querySelector('.lang-toggle');
    if (langToggle) {
        langToggle.setAttribute('aria-label', 'Switch language');
    }
    
    // Add keyboard navigation for dropdowns
    document.querySelectorAll('[data-dropdown]').forEach(trigger => {
        trigger.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                trigger.click();
            }
        });
    });
}

// Enhanced Theme Toggle
function toggleTheme() {
    const body = document.body;
    const themeIcon = document.getElementById('themeIcon');
    const currentTheme = body.getAttribute('data-theme');
    
    if (currentTheme === 'light') {
        body.setAttribute('data-theme', 'dark');
        themeIcon.className = 'fas fa-sun';
        localStorage.setItem('theme', 'dark');
        
        // Announce to screen readers
        announceToScreenReader('Dark theme activated');
    } else {
        body.setAttribute('data-theme', 'light');
        themeIcon.className = 'fas fa-moon';
        localStorage.setItem('theme', 'light');
        
        // Announce to screen readers
        announceToScreenReader('Light theme activated');
    }
}

// Enhanced Language Toggle
function toggleLanguage() {
    const langText = document.getElementById('langText');
    const currentLang = langText.textContent;
    
    if (currentLang === 'EN') {
        langText.textContent = 'বাং';
        document.documentElement.lang = 'bn';
        localStorage.setItem('language', 'bn');
        announceToScreenReader('Language switched to Bengali');
    } else {
        langText.textContent = 'EN';
        document.documentElement.lang = 'en';
        localStorage.setItem('language', 'en');
        announceToScreenReader('Language switched to English');
    }
}

// Screen Reader Announcements
function announceToScreenReader(message) {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    
    document.body.appendChild(announcement);
    
    setTimeout(() => {
        document.body.removeChild(announcement);
    }, 1000);
}

// Utility Functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// Export functions for global use
window.toggleTheme = toggleTheme;
window.toggleLanguage = toggleLanguage;
window.toggleMobileMenu = function() {
    const navMenu = document.getElementById('navMenu');
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    
    if (navMenu && mobileMenuToggle) {
        navMenu.classList.toggle('active');
        const isExpanded = navMenu.classList.contains('active');
        mobileMenuToggle.setAttribute('aria-expanded', isExpanded);
        
        const icon = mobileMenuToggle.querySelector('i');
        icon.className = isExpanded ? 'fas fa-times' : 'fas fa-bars';
    }
};
