    <!-- Main Footer -->
    <footer class="footer-main">
        <div class="footer-container">
            <!-- Footer Top Section -->
            <div class="footer-top">
                <div class="footer-grid">
                    <!-- Company Info Section -->
                    <div class="footer-section">
                        <div class="footer-logo">
                            <div class="logo-icon">
                                <i class="fas fa-briefcase"></i>
                            </div>
                            <span class="logo-text">JobSpace</span>
                        </div>
                        <p class="footer-description">
                            Your gateway to professional opportunities and career growth. Connect with top employers and find your dream job.
                        </p>
                        <div class="social-links">
                            <a href="#" class="social-link" title="Facebook">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="#" class="social-link" title="Twitter">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" class="social-link" title="LinkedIn">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                            <a href="#" class="social-link" title="Instagram">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="#" class="social-link" title="YouTube">
                                <i class="fab fa-youtube"></i>
                            </a>
                        </div>
                    </div>
                    
                    <!-- Quick Links Section -->
                    <div class="footer-section">
                        <h3 class="footer-title">Quick Links</h3>
                        <ul class="footer-links">
                            <li><a href="/">Home</a></li>
                            <li><a href="/jobs">Browse Jobs</a></li>
                            <li><a href="/companies">Companies</a></li>
                            <li><a href="/about">About Us</a></li>
                            <li><a href="/contact">Contact</a></li>
                            <li><a href="/help">Help Center</a></li>
                        </ul>
                    </div>
                    
                    <!-- Job Categories Section -->
                    <div class="footer-section">
                        <h3 class="footer-title">Job Categories</h3>
                        <ul class="footer-links">
                            <li><a href="/jobs?category=technology">Technology</a></li>
                            <li><a href="/jobs?category=marketing">Marketing</a></li>
                            <li><a href="/jobs?category=finance">Finance</a></li>
                            <li><a href="/jobs?category=healthcare">Healthcare</a></li>
                            <li><a href="/jobs?category=education">Education</a></li>
                            <li><a href="/jobs?category=design">Design</a></li>
                        </ul>
                    </div>
                    
                    <!-- Resources Section -->
                    <div class="footer-section">
                        <h3 class="footer-title">Resources</h3>
                        <ul class="footer-links">
                            <li><a href="/blog">Career Blog</a></li>
                            <li><a href="/resume-builder">Resume Builder</a></li>
                            <li><a href="/interview-tips">Interview Tips</a></li>
                            <li><a href="/salary-guide">Salary Guide</a></li>
                            <li><a href="/career-advice">Career Advice</a></li>
                            <li><a href="/faq">FAQ</a></li>
                        </ul>
                    </div>
                    
                    <!-- Newsletter Section -->
                    <div class="footer-section">
                        <h3 class="footer-title">Stay Updated</h3>
                        <p class="newsletter-description">
                            Subscribe to our newsletter for the latest job opportunities and career tips.
                        </p>
                        <form class="newsletter-form" onsubmit="subscribeNewsletter(event)">
                            <div class="input-group">
                                <input type="email" class="newsletter-input" placeholder="Enter your email" required>
                                <button type="submit" class="newsletter-btn">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </form>
                        <div class="app-downloads">
                            <p class="download-text">Download Our App</p>
                            <div class="download-buttons">
                                <a href="#" class="download-btn">
                                    <i class="fab fa-apple"></i>
                                    <span>App Store</span>
                                </a>
                                <a href="#" class="download-btn">
                                    <i class="fab fa-google-play"></i>
                                    <span>Play Store</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Footer Bottom Section -->
            <div class="footer-bottom">
                <div class="footer-bottom-content">
                    <div class="copyright">
                        <p>&copy; <?= date('Y') ?> JobSpace. All rights reserved.</p>
                    </div>
                    <div class="footer-bottom-links">
                        <a href="/privacy">Privacy Policy</a>
                        <a href="/terms">Terms of Service</a>
                        <a href="/cookies">Cookie Policy</a>
                        <a href="/sitemap">Sitemap</a>
                    </div>
                    <div class="footer-info">
                        <span class="made-with">Made with <i class="fas fa-heart"></i> in Bangladesh</span>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button class="back-to-top" onclick="scrollToTop()" title="Back to Top">
        <i class="fas fa-chevron-up"></i>
    </button>

    <!-- Footer Styles -->
    <style>
        .footer-main {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            color: #e2e8f0;
            margin-top: auto;
        }
        
        .footer-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }
        
        .footer-top {
            padding: 3rem 0 2rem;
        }
        
        .footer-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }
        
        .footer-section {
            display: flex;
            flex-direction: column;
        }
        
        .footer-logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 1rem;
        }
        
        .footer-logo .logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }
        
        .footer-logo .logo-text {
            font-size: 1.5rem;
            font-weight: 700;
            color: white;
        }
        
        .footer-description {
            color: #94a3b8;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }
        
        .social-links {
            display: flex;
            gap: 0.75rem;
        }
        
        .social-link {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #e2e8f0;
            text-decoration: none;
            transition: var(--transition);
        }
        
        .social-link:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }
        
        .footer-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
            margin-bottom: 1rem;
        }
        
        .footer-links {
            list-style: none;
            padding: 0;
        }
        
        .footer-links li {
            margin-bottom: 0.5rem;
        }
        
        .footer-links a {
            color: #94a3b8;
            text-decoration: none;
            transition: var(--transition);
        }
        
        .footer-links a:hover {
            color: var(--primary-color);
            padding-left: 0.5rem;
        }
        
        .newsletter-description {
            color: #94a3b8;
            margin-bottom: 1rem;
            line-height: 1.5;
        }
        
        .newsletter-form {
            margin-bottom: 1.5rem;
        }
        
        .input-group {
            display: flex;
            border-radius: 8px;
            overflow: hidden;
            background: rgba(255, 255, 255, 0.1);
        }
        
        .newsletter-input {
            flex: 1;
            padding: 0.75rem 1rem;
            border: none;
            background: transparent;
            color: white;
            outline: none;
        }
        
        .newsletter-input::placeholder {
            color: #94a3b8;
        }
        
        .newsletter-btn {
            padding: 0.75rem 1rem;
            background: var(--primary-color);
            border: none;
            color: white;
            cursor: pointer;
            transition: var(--transition);
        }
        
        .newsletter-btn:hover {
            background: #1d4ed8;
        }
        
        .app-downloads {
            margin-top: 1rem;
        }
        
        .download-text {
            color: #94a3b8;
            margin-bottom: 0.75rem;
            font-size: 0.9rem;
        }
        
        .download-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        
        .download-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            color: #e2e8f0;
            text-decoration: none;
            font-size: 0.85rem;
            transition: var(--transition);
        }
        
        .download-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }
        
        .footer-bottom {
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding: 1.5rem 0;
        }
        
        .footer-bottom-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .copyright {
            color: #94a3b8;
            font-size: 0.9rem;
        }
        
        .footer-bottom-links {
            display: flex;
            gap: 1.5rem;
            flex-wrap: wrap;
        }
        
        .footer-bottom-links a {
            color: #94a3b8;
            text-decoration: none;
            font-size: 0.9rem;
            transition: var(--transition);
        }
        
        .footer-bottom-links a:hover {
            color: var(--primary-color);
        }
        
        .footer-info {
            color: #94a3b8;
            font-size: 0.9rem;
        }
        
        .made-with .fa-heart {
            color: #ef4444;
            animation: heartbeat 1.5s ease-in-out infinite;
        }
        
        .back-to-top {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 50px;
            height: 50px;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            opacity: 0;
            visibility: hidden;
            transition: var(--transition);
            z-index: 1000;
        }
        
        .back-to-top.visible {
            opacity: 1;
            visibility: visible;
        }
        
        .back-to-top:hover {
            background: #1d4ed8;
            transform: translateY(-2px);
        }
        
        @keyframes heartbeat {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        
        /* Mobile Responsive */
        @media (max-width: 768px) {
            .footer-grid {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
            
            .footer-bottom-content {
                flex-direction: column;
                text-align: center;
                gap: 1rem;
            }
            
            .footer-bottom-links {
                justify-content: center;
            }
            
            .download-buttons {
                justify-content: center;
            }
            
            .back-to-top {
                bottom: 1rem;
                right: 1rem;
                width: 45px;
                height: 45px;
            }
        }
        
        @media (max-width: 480px) {
            .footer-top {
                padding: 2rem 0 1.5rem;
            }
            
            .social-links {
                justify-content: center;
            }
            
            .download-buttons {
                flex-direction: column;
            }
        }
    </style>

    <!-- Footer Scripts -->
    <script>
        // Newsletter Subscription
        function subscribeNewsletter(event) {
            event.preventDefault();
            const email = event.target.querySelector('.newsletter-input').value;
            
            // Here you would typically send the email to your backend
            alert('Thank you for subscribing! We\'ll keep you updated with the latest opportunities.');
            event.target.reset();
        }
        
        // Back to Top Functionality
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }
        
        // Show/Hide Back to Top Button
        window.addEventListener('scroll', function() {
            const backToTopBtn = document.querySelector('.back-to-top');
            if (window.pageYOffset > 300) {
                backToTopBtn.classList.add('visible');
            } else {
                backToTopBtn.classList.remove('visible');
            }
        });
        
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>

    <!-- JavaScript Dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/assets/js/main.js"></script>
</body>
</html>
