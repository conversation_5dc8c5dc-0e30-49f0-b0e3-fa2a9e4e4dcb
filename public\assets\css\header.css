/* Header Additional Styles */

/* Mobile Navigation Styles */
@media (max-width: 768px) {
    .nav-menu {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(10px);
        border-top: 1px solid var(--border-color);
        flex-direction: column;
        padding: 1rem;
        gap: 0;
        box-shadow: var(--shadow-md);
        transform: translateY(-10px);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }
    
    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }
    
    .nav-item {
        width: 100%;
        border-bottom: 1px solid var(--border-color);
    }
    
    .nav-item:last-child {
        border-bottom: none;
    }
    
    .nav-item a {
        display: block;
        padding: 1rem;
        width: 100%;
        text-align: left;
    }
}

/* Search Dropdown Styles */
.search-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid var(--border-color);
    border-top: none;
    border-radius: 0 0 8px 8px;
    box-shadow: var(--shadow-md);
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.search-dropdown.active {
    display: block;
}

.search-result-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: var(--transition);
}

.search-result-item:hover {
    background: var(--light-color);
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-result-title {
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: 0.25rem;
}

.search-result-meta {
    font-size: 0.85rem;
    color: var(--secondary-color);
}

/* Header Scroll Effect */
.header-main.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-md);
}

[data-theme="dark"] .header-main.scrolled {
    background: rgba(30, 41, 59, 0.98);
}

/* User Menu Dropdown (for logged in users) */
.user-menu {
    position: relative;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    border: 2px solid var(--border-color);
    transition: var(--transition);
}

.user-avatar:hover {
    border-color: var(--primary-color);
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: var(--shadow-md);
    min-width: 200px;
    padding: 0.5rem 0;
    z-index: 1000;
    display: none;
}

.user-dropdown.active {
    display: block;
}

.user-dropdown-item {
    display: block;
    padding: 0.75rem 1rem;
    color: var(--dark-color);
    text-decoration: none;
    transition: var(--transition);
}

.user-dropdown-item:hover {
    background: var(--light-color);
    color: var(--primary-color);
}

.user-dropdown-divider {
    height: 1px;
    background: var(--border-color);
    margin: 0.5rem 0;
}

/* Notification Badge */
.notification-badge {
    position: relative;
}

.notification-badge::after {
    content: '';
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    background: #ef4444;
    border-radius: 50%;
    border: 2px solid white;
}

/* Language Selector Enhanced */
.lang-dropdown {
    position: relative;
}

.lang-options {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    box-shadow: var(--shadow-md);
    min-width: 120px;
    padding: 0.5rem 0;
    z-index: 1000;
    display: none;
}

.lang-options.active {
    display: block;
}

.lang-option {
    display: block;
    padding: 0.5rem 1rem;
    color: var(--dark-color);
    text-decoration: none;
    transition: var(--transition);
}

.lang-option:hover {
    background: var(--light-color);
    color: var(--primary-color);
}

.lang-option.active {
    background: var(--primary-color);
    color: white;
}

/* Loading States */
.search-input.loading {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 24 24'%3E%3Cpath fill='%236b7280' d='M12,1A11,11,0,1,0,23,12,11,11,0,0,0,12,1Zm0,19a8,8,0,1,1,8-8A8,8,0,0,1,12,20Z' opacity='.25'/%3E%3Cpath fill='%236b7280' d='M12,4a8,8,0,0,1,7.89,6.7A1.53,1.53,0,0,0,21.38,12h0a1.5,1.5,0,0,0,1.48-1.75,11,11,0,0,0-21.72,0A1.5,1.5,0,0,0,2.62,12h0a1.53,1.53,0,0,0,1.49-1.3A8,8,0,0,1,12,4Z'%3E%3CanimateTransform attributeName='transform' dur='0.75s' repeatCount='indefinite' type='rotate' values='0 12 12;360 12 12'/%3E%3C/path%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 1rem center;
    background-size: 16px;
}

/* Accessibility Improvements */
.header-main *:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

.slide-down {
    animation: slideDown 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideDown {
    from { 
        opacity: 0;
        transform: translateY(-10px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

/* Print Styles */
@media print {
    .header-main {
        display: none;
    }
}
