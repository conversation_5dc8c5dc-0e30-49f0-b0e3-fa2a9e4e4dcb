<!DOCTYPE html>
<html lang="<?= app()->getLocale() ?? 'en' ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title><?= $title ?? 'JobSpace - Professional Career Platform' ?></title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="<?= $description ?? 'JobSpace - Your gateway to professional opportunities and career growth' ?>">
    <meta name="keywords" content="<?= $keywords ?? 'jobs, career, employment, professional, opportunities' ?>">
    <meta name="author" content="JobSpace">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?= $og_title ?? $title ?? 'JobSpace' ?>">
    <meta property="og:description" content="<?= $og_description ?? $description ?? 'Professional Career Platform' ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?= $og_url ?? (isset($_SERVER['REQUEST_URI']) ? 'https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'] : '') ?>">
    <meta property="og:image" content="<?= $og_image ?? '/assets/images/jobspace-og-image.jpg' ?>">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:site_name" content="JobSpace">
    <meta property="og:locale" content="en_US">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?= $og_title ?? $title ?? 'JobSpace' ?>">
    <meta name="twitter:description" content="<?= $og_description ?? $description ?? 'Professional Career Platform' ?>">
    <meta name="twitter:image" content="<?= $og_image ?? '/assets/images/jobspace-og-image.jpg' ?>">

    <!-- Additional SEO Meta Tags -->
    <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">
    <meta name="googlebot" content="index, follow">
    <link rel="canonical" href="<?= $og_url ?? (isset($_SERVER['REQUEST_URI']) ? 'https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'] : '') ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/icons/favicon.ico">
    <link rel="apple-touch-icon" href="/assets/icons/apple-touch-icon.png">
    
    <!-- CSS Dependencies -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="/assets/css/header.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --accent-color: #f59e0b;
            --dark-color: #1e293b;
            --light-color: #f8fafc;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            --transition: all 0.3s ease;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--dark-color);
        }
        
        .header-main {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--border-color);
            position: sticky;
            top: 0;
            z-index: 1000;
            transition: var(--transition);
        }
        
        .header-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }
        
        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 70px;
        }
        
        .logo-section {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        
        .logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            font-weight: 600;
        }
        
        .logo-text {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--dark-color);
            text-decoration: none;
        }
        
        .nav-menu {
            display: flex;
            align-items: center;
            gap: 2rem;
            list-style: none;
        }
        
        .nav-item a {
            color: var(--secondary-color);
            text-decoration: none;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            transition: var(--transition);
        }
        
        .nav-item a:hover {
            color: var(--primary-color);
            background: var(--light-color);
        }
        
        .header-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .search-container {
            position: relative;
            display: flex;
            align-items: center;
        }
        
        .search-input {
            width: 300px;
            padding: 0.5rem 1rem 0.5rem 2.5rem;
            border: 1px solid var(--border-color);
            border-radius: 25px;
            font-size: 0.9rem;
            transition: var(--transition);
        }
        
        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        .search-icon {
            position: absolute;
            left: 0.75rem;
            color: var(--secondary-color);
        }
        
        .theme-toggle, .lang-toggle {
            background: none;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            padding: 0.5rem;
            cursor: pointer;
            transition: var(--transition);
            color: var(--secondary-color);
        }
        
        .theme-toggle:hover, .lang-toggle:hover {
            background: var(--light-color);
            color: var(--primary-color);
        }
        
        .auth-buttons {
            display: flex;
            gap: 0.5rem;
        }
        
        .btn-login {
            padding: 0.5rem 1.5rem;
            border: 1px solid var(--primary-color);
            background: transparent;
            color: var(--primary-color);
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
        }
        
        .btn-login:hover {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-register {
            padding: 0.5rem 1.5rem;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
        }
        
        .btn-register:hover {
            background: #1d4ed8;
            color: white;
        }
        
        .mobile-menu-toggle {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--dark-color);
            cursor: pointer;
        }
        
        /* Dark Theme */
        [data-theme="dark"] {
            --dark-color: #f8fafc;
            --light-color: #1e293b;
            --border-color: #334155;
        }
        
        [data-theme="dark"] .header-main {
            background: rgba(30, 41, 59, 0.95);
            border-bottom-color: var(--border-color);
        }
        
        /* Mobile Responsive */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }
            
            .search-input {
                width: 200px;
            }
            
            .mobile-menu-toggle {
                display: block;
            }
            
            .auth-buttons {
                gap: 0.25rem;
            }
            
            .btn-login, .btn-register {
                padding: 0.4rem 1rem;
                font-size: 0.9rem;
            }
        }
        
        @media (max-width: 480px) {
            .search-container {
                display: none;
            }
            
            .header-actions {
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body data-theme="light">
    <header class="header-main">
        <div class="header-container">
            <div class="header-content">
                <!-- Logo Section -->
                <div class="logo-section">
                    <div class="logo-icon">
                        <i class="fas fa-briefcase"></i>
                    </div>
                    <a href="/" class="logo-text">JobSpace</a>
                </div>
                
                <!-- Navigation Menu -->
                <nav class="nav-menu" id="navMenu">
                    <li class="nav-item"><a href="/">Home</a></li>
                    <li class="nav-item"><a href="/jobs">Jobs</a></li>
                    <li class="nav-item"><a href="/companies">Companies</a></li>
                    <li class="nav-item"><a href="/about">About</a></li>
                    <li class="nav-item"><a href="/contact">Contact</a></li>
                </nav>
                
                <!-- Header Actions -->
                <div class="header-actions">
                    <!-- Search -->
                    <div class="search-container">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="search-input" placeholder="Search jobs, companies...">
                    </div>
                    
                    <!-- Theme Toggle -->
                    <button class="theme-toggle" onclick="toggleTheme()" title="Toggle Dark/Light Mode">
                        <i class="fas fa-moon" id="themeIcon"></i>
                    </button>
                    
                    <!-- Language Toggle -->
                    <button class="lang-toggle" onclick="toggleLanguage()" title="Switch Language">
                        <i class="fas fa-globe"></i>
                        <span id="langText">EN</span>
                    </button>
                    
                    <!-- Auth Buttons -->
                    <div class="auth-buttons">
                        <a href="/auth/login" class="btn-login">
                            <i class="fas fa-sign-in-alt"></i> Login
                        </a>
                        <a href="/auth/register" class="btn-register">
                            <i class="fas fa-user-plus"></i> Register
                        </a>
                    </div>
                    
                    <!-- Mobile Menu Toggle -->
                    <button class="mobile-menu-toggle" onclick="toggleMobileMenu()">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <script>
        // Theme Toggle Functionality
        function toggleTheme() {
            const body = document.body;
            const themeIcon = document.getElementById('themeIcon');
            const currentTheme = body.getAttribute('data-theme');
            
            if (currentTheme === 'light') {
                body.setAttribute('data-theme', 'dark');
                themeIcon.className = 'fas fa-sun';
                localStorage.setItem('theme', 'dark');
            } else {
                body.setAttribute('data-theme', 'light');
                themeIcon.className = 'fas fa-moon';
                localStorage.setItem('theme', 'light');
            }
        }
        
        // Language Toggle Functionality
        function toggleLanguage() {
            const langText = document.getElementById('langText');
            const currentLang = langText.textContent;
            
            if (currentLang === 'EN') {
                langText.textContent = 'বাং';
                localStorage.setItem('language', 'bn');
            } else {
                langText.textContent = 'EN';
                localStorage.setItem('language', 'en');
            }
        }
        
        // Mobile Menu Toggle
        function toggleMobileMenu() {
            const navMenu = document.getElementById('navMenu');
            navMenu.style.display = navMenu.style.display === 'flex' ? 'none' : 'flex';
        }
        
        // Load saved preferences
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            const savedLang = localStorage.getItem('language') || 'en';
            
            document.body.setAttribute('data-theme', savedTheme);
            document.getElementById('themeIcon').className = savedTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            document.getElementById('langText').textContent = savedLang === 'bn' ? 'বাং' : 'EN';
        });
    </script>
