<?php
/**
 * Hero Section - JobSpace Landing
 * Professional, SEO-optimized, high-performance hero section
 */

// Dynamic data for hero section
$heroData = [
    'title' => 'Earn Real Money Through Fun Quizzes',
    'subtitle' => 'Join JobSpace - where knowledge meets rewards! Play engaging quizzes, earn real money and coins, participate in exciting challenges. Start your earning journey today while having fun!',
    'features' => [
        'Dual earning system (Cash + Coins)',
        'Instant payments & withdrawals',
        'Fun quizzes & social activities',
        'Daily challenges & bonuses'
    ],
    'stats' => [
        ['number' => '৳1,00,000+', 'label' => 'Total Paid', 'icon' => 'fas fa-money-bill-wave'],
        ['number' => '5,000+', 'label' => 'Active Users', 'icon' => 'fas fa-users'],
        ['number' => '50,000+', 'label' => 'Quizzes Completed', 'icon' => 'fas fa-brain'],
        ['number' => '99%', 'label' => 'User Satisfaction', 'icon' => 'fas fa-star']
    ],
    'job_categories' => [
        ['icon' => 'fas fa-laptop-code', 'title' => 'IT & Technology', 'desc' => 'Software, Web, Mobile Dev'],
        ['icon' => 'fas fa-chart-line', 'title' => 'Marketing & Sales', 'desc' => 'Digital Marketing, Sales'],
        ['icon' => 'fas fa-user-tie', 'title' => 'Management', 'desc' => 'Project, HR, Operations'],
        ['icon' => 'fas fa-graduation-cap', 'title' => 'Education', 'desc' => 'Teaching, Training, Research']
    ]
];
?>

<!-- Hero Section with Professional Gradient Background -->
<section class="hero-section position-relative overflow-hidden" style="background: linear-gradient(135deg, #2563eb 0%, #1e40af 50%, #1e3a8a 100%); min-height: 100vh;">
    <!-- Animated Background Elements -->
    <div class="position-absolute w-100 h-100" style="z-index: 1;">
        <div class="floating-elements">
            <i class="fas fa-coins position-absolute" style="top: 10%; left: 10%; font-size: 2rem; color: rgba(255,255,255,0.1); animation: float 6s ease-in-out infinite;"></i>
            <i class="fas fa-brain position-absolute" style="top: 20%; right: 15%; font-size: 1.5rem; color: rgba(255,255,255,0.1); animation: float 8s ease-in-out infinite reverse;"></i>
            <i class="fas fa-trophy position-absolute" style="bottom: 30%; left: 20%; font-size: 1.8rem; color: rgba(255,255,255,0.1); animation: float 7s ease-in-out infinite;"></i>
            <i class="fas fa-star position-absolute" style="bottom: 20%; right: 10%; font-size: 1.3rem; color: rgba(255,255,255,0.1); animation: float 5s ease-in-out infinite reverse;"></i>
        </div>
    </div>

    <div class="container position-relative" style="z-index: 2;">
        <div class="row align-items-center min-vh-100 py-5">
            <!-- Left Content -->
            <div class="col-lg-6 text-white">
                <div class="mb-4">
                    <span class="badge bg-warning text-dark px-3 py-2 rounded-pill mb-3">
                        <i class="fas fa-fire me-1"></i> #1 Earning Platform in Bangladesh
                    </span>
                </div>

                <h1 class="display-4 fw-bold mb-4 lh-1">
                    <?= htmlspecialchars($heroData['title']) ?>
                </h1>

                <p class="lead mb-4 opacity-90">
                    <?= htmlspecialchars($heroData['subtitle']) ?>
                </p>

                <!-- Key Features -->
                <div class="row mb-4">
                    <?php foreach ($heroData['features'] as $feature): ?>
                    <div class="col-md-6 mb-2">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-check-circle text-warning me-2"></i>
                            <small class="opacity-90"><?= htmlspecialchars($feature) ?></small>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>

                <!-- CTA Buttons -->
                <div class="d-flex flex-wrap gap-3 mb-5">
                    <a href="/register" class="btn btn-warning btn-lg px-4 py-3 fw-bold text-dark">
                        <i class="fas fa-rocket me-2"></i>Start Earning Free
                    </a>
                    <a href="/about" class="btn btn-outline-light btn-lg px-4 py-3">
                        <i class="fas fa-play me-2"></i>Watch Demo
                    </a>
                </div>

                <!-- Stats -->
                <div class="row text-center">
                    <?php foreach ($heroData['stats'] as $stat): ?>
                    <div class="col-6 col-md-3 mb-3">
                        <div class="stat-item">
                            <i class="<?= $stat['icon'] ?> text-warning mb-2" style="font-size: 1.5rem;"></i>
                            <div class="h4 mb-0 fw-bold"><?= htmlspecialchars($stat['number']) ?></div>
                            <small class="opacity-75"><?= htmlspecialchars($stat['label']) ?></small>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Right Content - Live Quiz Demo -->
            <div class="col-lg-6">
                <div class="bg-white rounded-4 p-4 shadow-lg position-relative overflow-hidden">
                    <!-- Demo Badge -->
                    <div class="position-absolute top-0 end-0 m-3">
                        <span class="badge bg-success px-2 py-1 rounded-pill">
                            <i class="fas fa-play me-1"></i>Live Demo
                        </span>
                    </div>

                    <!-- Balance Display -->
                    <div class="text-center mb-4">
                        <div class="d-flex justify-content-center align-items-center gap-3 mb-3">
                            <div class="balance-card bg-gradient-primary text-white px-3 py-2 rounded-3">
                                <small class="d-block opacity-75">Your Balance</small>
                                <h5 class="mb-0 fw-bold" id="demoBalance">৳<span id="balanceAmount">1,250</span></h5>
                            </div>
                            <div class="balance-card bg-gradient-warning text-dark px-3 py-2 rounded-3">
                                <small class="d-block opacity-75">Coins</small>
                                <h5 class="mb-0 fw-bold" id="demoCoins"><i class="fas fa-coins"></i> <span id="coinAmount">850</span></h5>
                            </div>
                        </div>
                        <p class="text-muted small mb-0">Play quiz and watch your earnings grow!</p>
                    </div>

                    <!-- Quiz Demo Container -->
                    <div id="quizDemo" class="quiz-demo-container">
                        <!-- Question Display -->
                        <div class="quiz-question mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <span class="badge bg-info text-white">Question <span id="questionNumber">1</span>/5</span>
                                <div class="quiz-timer">
                                    <i class="fas fa-clock text-warning"></i>
                                    <span id="timer" class="fw-bold">15</span>s
                                </div>
                            </div>

                            <h6 class="text-dark mb-3" id="questionText">বাংলাদেশের রাজধানী কোনটি?</h6>

                            <!-- Answer Options -->
                            <div class="quiz-options">
                                <button class="quiz-option btn btn-outline-primary w-100 mb-2 text-start" data-answer="wrong">
                                    <span class="option-letter">A.</span> চট্টগ্রাম
                                </button>
                                <button class="quiz-option btn btn-outline-primary w-100 mb-2 text-start" data-answer="correct">
                                    <span class="option-letter">B.</span> ঢাকা
                                </button>
                                <button class="quiz-option btn btn-outline-primary w-100 mb-2 text-start" data-answer="wrong">
                                    <span class="option-letter">C.</span> সিলেট
                                </button>
                                <button class="quiz-option btn btn-outline-primary w-100 mb-2 text-start" data-answer="wrong">
                                    <span class="option-letter">D.</span> রাজশাহী
                                </button>
                            </div>
                        </div>

                        <!-- Earning Animation -->
                        <div id="earningAnimation" class="earning-animation text-center" style="display: none;">
                            <div class="earning-popup bg-success text-white p-3 rounded-3 mb-3">
                                <i class="fas fa-check-circle fa-2x mb-2"></i>
                                <h6 class="mb-1">সঠিক উত্তর! 🎉</h6>
                                <p class="mb-0">আপনি পেয়েছেন: <strong>+৳50 + 25 Coins</strong></p>
                            </div>
                        </div>

                        <!-- Progress Bar -->
                        <div class="quiz-progress mb-3">
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-success" id="progressBar" style="width: 20%"></div>
                            </div>
                            <small class="text-muted">Quiz Progress</small>
                        </div>

                        <!-- Action Buttons -->
                        <div class="text-center">
                            <button id="startQuizDemo" class="btn btn-success btn-lg px-4 fw-bold">
                                <i class="fas fa-play me-2"></i>Start Quiz Demo
                            </button>
                            <button id="nextQuestion" class="btn btn-primary btn-lg px-4 fw-bold" style="display: none;">
                                <i class="fas fa-arrow-right me-2"></i>Next Question
                            </button>
                        </div>
                    </div>

                    <!-- Quick Stats -->
                    <div class="mt-4 p-3 bg-light rounded-3">
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="stat-mini">
                                    <h6 class="text-success mb-0">৳50</h6>
                                    <small class="text-muted">Per Correct</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stat-mini">
                                    <h6 class="text-warning mb-0">25 <i class="fas fa-coins"></i></h6>
                                    <small class="text-muted">Bonus Coins</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stat-mini">
                                    <h6 class="text-info mb-0">5 Min</h6>
                                    <small class="text-muted">Quick Play</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Call to Action -->
                    <div class="mt-3 text-center">
                        <a href="/register" class="btn btn-warning btn-sm fw-bold text-dark">
                            <i class="fas fa-rocket me-1"></i>Start Earning Real Money
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Quiz Demo Styles & Animations -->
<style>
@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes balanceIncrease {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); color: #28a745; }
    100% { transform: scale(1); }
}

@keyframes coinBounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

@keyframes correctAnswer {
    0% { background-color: #28a745; transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.balance-card {
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.balance-card.animate {
    animation: balanceIncrease 0.6s ease;
    border-color: #28a745;
}

.quiz-option {
    transition: all 0.3s ease;
    border: 2px solid #dee2e6;
}

.quiz-option:hover {
    border-color: #007bff;
    transform: translateX(5px);
}

.quiz-option.correct {
    background-color: #28a745 !important;
    border-color: #28a745 !important;
    color: white !important;
    animation: correctAnswer 0.5s ease;
}

.quiz-option.wrong {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    color: white !important;
}

.quiz-option:disabled {
    pointer-events: none;
}

.earning-animation {
    animation: slideInUp 0.5s ease;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.earning-popup {
    animation: pulse 0.5s ease;
}

@keyframes pulse {
    0% { transform: scale(0.8); opacity: 0; }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); opacity: 1; }
}

.quiz-timer {
    font-size: 1.1rem;
    color: #ff6b35;
}

.option-letter {
    font-weight: bold;
    margin-right: 8px;
}

.stat-item {
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: scale(1.05);
}

.hero-section .btn {
    transition: all 0.3s ease;
}

.hero-section .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

.bg-gradient-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
}

.bg-gradient-warning {
    background: linear-gradient(45deg, #ffc107, #e0a800);
}

.progress-bar {
    transition: width 0.5s ease;
}
</style>

<!-- Quiz Demo JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const quizQuestions = [
        {
            question: "বাংলাদেশের রাজধানী কোনটি?",
            options: ["চট্টগ্রাম", "ঢাকা", "সিলেট", "রাজশাহী"],
            correct: 1,
            reward: { money: 50, coins: 25 }
        },
        {
            question: "বাংলাদেশের জাতীয় ফুল কোনটি?",
            options: ["গোলাপ", "শাপলা", "জুঁই", "বেলি"],
            correct: 1,
            reward: { money: 50, coins: 25 }
        },
        {
            question: "বাংলাদেশের মুদ্রার নাম কি?",
            options: ["রুপি", "ডলার", "টাকা", "পাউন্ড"],
            correct: 2,
            reward: { money: 50, coins: 25 }
        },
        {
            question: "বাংলাদেশের জাতীয় কবি কে?",
            options: ["রবীন্দ্রনাথ ঠাকুর", "কাজী নজরুল ইসলাম", "জীবনানন্দ দাশ", "মাইকেল মধুসূদন দত্ত"],
            correct: 1,
            reward: { money: 50, coins: 25 }
        },
        {
            question: "বাংলাদেশের স্বাধীনতা দিবস কবে?",
            options: ["২১ ফেব্রুয়ারি", "১৬ ডিসেম্বর", "২৬ মার্চ", "১৪ এপ্রিল"],
            correct: 2,
            reward: { money: 50, coins: 25 }
        }
    ];

    let currentQuestion = 0;
    let currentBalance = 1250;
    let currentCoins = 850;
    let timer;
    let timeLeft = 15;

    const startBtn = document.getElementById('startQuizDemo');
    const nextBtn = document.getElementById('nextQuestion');
    const questionText = document.getElementById('questionText');
    const questionNumber = document.getElementById('questionNumber');
    const timerElement = document.getElementById('timer');
    const balanceAmount = document.getElementById('balanceAmount');
    const coinAmount = document.getElementById('coinAmount');
    const progressBar = document.getElementById('progressBar');
    const earningAnimation = document.getElementById('earningAnimation');
    const quizOptions = document.querySelectorAll('.quiz-option');

    function startTimer() {
        timeLeft = 15;
        timer = setInterval(() => {
            timeLeft--;
            timerElement.textContent = timeLeft;

            if (timeLeft <= 5) {
                timerElement.style.color = '#dc3545';
            } else {
                timerElement.style.color = '#ff6b35';
            }

            if (timeLeft <= 0) {
                clearInterval(timer);
                // Auto select wrong answer if time runs out
                handleTimeUp();
            }
        }, 1000);
    }

    function handleTimeUp() {
        quizOptions.forEach(option => {
            option.disabled = true;
            if (option.dataset.answer !== 'correct') {
                option.classList.add('wrong');
            }
        });

        setTimeout(() => {
            nextQuestion();
        }, 2000);
    }

    function loadQuestion() {
        if (currentQuestion >= quizQuestions.length) {
            showCompletionMessage();
            return;
        }

        const question = quizQuestions[currentQuestion];
        questionText.textContent = question.question;
        questionNumber.textContent = currentQuestion + 1;

        quizOptions.forEach((option, index) => {
            option.textContent = '';
            option.innerHTML = `<span class="option-letter">${String.fromCharCode(65 + index)}.</span> ${question.options[index]}`;
            option.disabled = false;
            option.className = 'quiz-option btn btn-outline-primary w-100 mb-2 text-start';
            option.dataset.answer = index === question.correct ? 'correct' : 'wrong';
        });

        // Update progress
        const progress = ((currentQuestion + 1) / quizQuestions.length) * 100;
        progressBar.style.width = progress + '%';

        earningAnimation.style.display = 'none';
        nextBtn.style.display = 'none';

        startTimer();
    }

    function handleAnswer(selectedOption) {
        clearInterval(timer);

        quizOptions.forEach(option => {
            option.disabled = true;
            if (option.dataset.answer === 'correct') {
                option.classList.add('correct');
            } else if (option === selectedOption && option.dataset.answer === 'wrong') {
                option.classList.add('wrong');
            }
        });

        if (selectedOption.dataset.answer === 'correct') {
            // Show earning animation
            const reward = quizQuestions[currentQuestion].reward;
            currentBalance += reward.money;
            currentCoins += reward.coins;

            // Animate balance increase
            setTimeout(() => {
                balanceAmount.textContent = currentBalance.toLocaleString();
                coinAmount.textContent = currentCoins.toLocaleString();

                document.getElementById('demoBalance').classList.add('animate');
                document.getElementById('demoCoins').classList.add('animate');

                setTimeout(() => {
                    document.getElementById('demoBalance').classList.remove('animate');
                    document.getElementById('demoCoins').classList.remove('animate');
                }, 600);
            }, 500);

            earningAnimation.style.display = 'block';
        }

        setTimeout(() => {
            nextBtn.style.display = 'inline-block';
        }, 1500);
    }

    function nextQuestion() {
        currentQuestion++;
        if (currentQuestion < quizQuestions.length) {
            loadQuestion();
        } else {
            showCompletionMessage();
        }
    }

    function showCompletionMessage() {
        const quizContainer = document.querySelector('.quiz-question');
        quizContainer.innerHTML = `
            <div class="text-center">
                <i class="fas fa-trophy text-warning fa-3x mb-3"></i>
                <h5 class="text-success mb-3">Quiz Completed! 🎉</h5>
                <p class="text-muted mb-4">আপনি সফলভাবে quiz সম্পন্ন করেছেন!</p>
                <div class="row">
                    <div class="col-6">
                        <div class="bg-success text-white p-3 rounded">
                            <h6>Total Earned</h6>
                            <h4>৳${(currentBalance - 1250).toLocaleString()}</h4>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="bg-warning text-dark p-3 rounded">
                            <h6>Bonus Coins</h6>
                            <h4>${(currentCoins - 850).toLocaleString()} <i class="fas fa-coins"></i></h4>
                        </div>
                    </div>
                </div>
                <button onclick="restartDemo()" class="btn btn-primary mt-3">
                    <i class="fas fa-redo me-2"></i>Play Again
                </button>
            </div>
        `;

        nextBtn.style.display = 'none';
        earningAnimation.style.display = 'none';
    }

    window.restartDemo = function() {
        currentQuestion = 0;
        currentBalance = 1250;
        currentCoins = 850;
        balanceAmount.textContent = currentBalance.toLocaleString();
        coinAmount.textContent = currentCoins.toLocaleString();

        // Reset quiz container
        location.reload();
    }

    // Event listeners
    startBtn.addEventListener('click', function() {
        this.style.display = 'none';
        loadQuestion();
    });

    nextBtn.addEventListener('click', nextQuestion);

    quizOptions.forEach(option => {
        option.addEventListener('click', function() {
            if (!this.disabled) {
                handleAnswer(this);
            }
        });
    });
});
</script>
