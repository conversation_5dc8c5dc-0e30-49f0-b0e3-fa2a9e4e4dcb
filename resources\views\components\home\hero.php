<?php
/**
 * Hero Section - JobSpace Landing
 * Professional, SEO-optimized, high-performance hero section
 */

// Dynamic data for hero section
$heroData = [
    'title' => 'Find Your Dream Job in Bangladesh',
    'subtitle' => 'Join JobSpace - the leading professional career platform! Connect with top employers, discover thousands of job opportunities, and build your successful career. Your dream job is just one click away!',
    'features' => [
        '10,000+ Active Job Listings',
        'Top Companies & Startups',
        'Professional Career Guidance',
        'Free Resume Builder & Tips'
    ],
    'stats' => [
        ['number' => '10,000+', 'label' => 'Active Jobs', 'icon' => 'fas fa-briefcase'],
        ['number' => '5,000+', 'label' => 'Companies', 'icon' => 'fas fa-building'],
        ['number' => '50,000+', 'label' => 'Job Seekers', 'icon' => 'fas fa-users'],
        ['number' => '95%', 'label' => 'Success Rate', 'icon' => 'fas fa-star']
    ],
    'job_categories' => [
        ['icon' => 'fas fa-laptop-code', 'title' => 'IT & Technology', 'desc' => 'Software, Web, Mobile Dev'],
        ['icon' => 'fas fa-chart-line', 'title' => 'Marketing & Sales', 'desc' => 'Digital Marketing, Sales'],
        ['icon' => 'fas fa-user-tie', 'title' => 'Management', 'desc' => 'Project, HR, Operations'],
        ['icon' => 'fas fa-graduation-cap', 'title' => 'Education', 'desc' => 'Teaching, Training, Research']
    ]
];
?>

<!-- Hero Section with Professional Gradient Background -->
<section class="hero-section position-relative overflow-hidden" style="background: linear-gradient(135deg, #2563eb 0%, #1e40af 50%, #1e3a8a 100%); min-height: 100vh;">
    <!-- Animated Background Elements -->
    <div class="position-absolute w-100 h-100" style="z-index: 1;">
        <div class="floating-elements">
            <i class="fas fa-briefcase position-absolute" style="top: 10%; left: 10%; font-size: 2rem; color: rgba(255,255,255,0.1); animation: float 6s ease-in-out infinite;"></i>
            <i class="fas fa-building position-absolute" style="top: 20%; right: 15%; font-size: 1.5rem; color: rgba(255,255,255,0.1); animation: float 8s ease-in-out infinite reverse;"></i>
            <i class="fas fa-user-tie position-absolute" style="bottom: 30%; left: 20%; font-size: 1.8rem; color: rgba(255,255,255,0.1); animation: float 7s ease-in-out infinite;"></i>
            <i class="fas fa-chart-line position-absolute" style="bottom: 20%; right: 10%; font-size: 1.3rem; color: rgba(255,255,255,0.1); animation: float 5s ease-in-out infinite reverse;"></i>
        </div>
    </div>

    <div class="container position-relative" style="z-index: 2;">
        <div class="row align-items-center min-vh-100 py-5">
            <!-- Left Content -->
            <div class="col-lg-6 text-white">
                <div class="mb-4">
                    <span class="badge bg-warning text-dark px-3 py-2 rounded-pill mb-3">
                        <i class="fas fa-trophy me-1"></i> #1 Job Portal in Bangladesh
                    </span>
                </div>

                <h1 class="display-4 fw-bold mb-4 lh-1">
                    <?= htmlspecialchars($heroData['title']) ?>
                </h1>

                <p class="lead mb-4 opacity-90">
                    <?= htmlspecialchars($heroData['subtitle']) ?>
                </p>

                <!-- Key Features -->
                <div class="row mb-4">
                    <?php foreach ($heroData['features'] as $feature): ?>
                    <div class="col-md-6 mb-2">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-check-circle text-warning me-2"></i>
                            <small class="opacity-90"><?= htmlspecialchars($feature) ?></small>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>

                <!-- CTA Buttons -->
                <div class="d-flex flex-wrap gap-3 mb-5">
                    <a href="/register" class="btn btn-warning btn-lg px-4 py-3 fw-bold text-dark">
                        <i class="fas fa-user-plus me-2"></i>Join Free Today
                    </a>
                    <a href="/jobs" class="btn btn-outline-light btn-lg px-4 py-3">
                        <i class="fas fa-search me-2"></i>Browse Jobs
                    </a>
                </div>

                <!-- Stats -->
                <div class="row text-center">
                    <?php foreach ($heroData['stats'] as $stat): ?>
                    <div class="col-6 col-md-3 mb-3">
                        <div class="stat-item">
                            <i class="<?= $stat['icon'] ?> text-warning mb-2" style="font-size: 1.5rem;"></i>
                            <div class="h4 mb-0 fw-bold"><?= htmlspecialchars($stat['number']) ?></div>
                            <small class="opacity-75"><?= htmlspecialchars($stat['label']) ?></small>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Right Content - Job Categories -->
            <div class="col-lg-6">
                <div class="bg-white rounded-4 p-4 shadow-lg">
                    <div class="text-center mb-4">
                        <h3 class="h4 text-dark mb-2">Popular Job Categories</h3>
                        <p class="text-muted small">Find opportunities in your field</p>
                    </div>

                    <div class="row g-3">
                        <?php foreach ($heroData['job_categories'] as $category): ?>
                        <div class="col-6">
                            <div class="job-category text-center p-3 rounded-3 border h-100">
                                <div class="mb-3">
                                    <i class="<?= $category['icon'] ?> text-primary" style="font-size: 2rem;"></i>
                                </div>
                                <h6 class="fw-bold text-dark mb-2"><?= htmlspecialchars($category['title']) ?></h6>
                                <p class="small text-muted mb-0"><?= htmlspecialchars($category['desc']) ?></p>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- Quick Start -->
                    <div class="mt-4 p-3 bg-light rounded-3">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h6 class="mb-1 text-dark">Ready to find your dream job?</h6>
                                <small class="text-muted">Join thousands of professionals</small>
                            </div>
                            <a href="/register" class="btn btn-primary btn-sm">
                                Get Started <i class="fas fa-arrow-right ms-1"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Floating Animation CSS -->
<style>
@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.earning-method {
    transition: all 0.3s ease;
    background: rgba(255,255,255,0.8);
}

.earning-method:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    background: white;
}

.stat-item {
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: scale(1.05);
}

.hero-section .btn {
    transition: all 0.3s ease;
}

.hero-section .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}
</style>
