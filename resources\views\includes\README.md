# JobSpace Header & Footer Components

This directory contains professional, responsive header and footer components for the JobSpace application.

## 📁 Structure

```
includes/
├── header/
│   └── public-header.php    # Main header component
├── footer/
│   └── public-footer.php    # Main footer component
└── README.md               # This documentation
```

## 🎯 Features

### Header Features
- **Responsive Design**: Works perfectly on desktop, tablet, and mobile
- **Dynamic Navigation**: Collapsible mobile menu with smooth animations
- **Search Functionality**: Real-time search with dropdown suggestions
- **Theme Toggle**: Dark/Light mode with localStorage persistence
- **Language Switch**: English/Bengali toggle with localStorage
- **Professional Styling**: Modern gradient logo, clean typography
- **Accessibility**: ARIA labels, keyboard navigation, screen reader support
- **SEO Optimized**: Proper meta tags, Open Graph support

### Footer Features
- **5-Section Layout**: Company info, Quick links, Job categories, Resources, Newsletter
- **Social Media Links**: Facebook, Twitter, LinkedIn, Instagram, YouTube
- **Newsletter Subscription**: Email capture with validation
- **App Download Links**: iOS and Android app promotion
- **Back to Top Button**: Smooth scroll functionality
- **Responsive Grid**: Adapts beautifully to all screen sizes
- **Professional Design**: Modern dark theme with gradient background

## 🚀 Usage

### Basic Implementation

1. **Using the Layout System**:
```php
<?php
// Set page-specific variables
$title = 'Your Page Title';
$description = 'Your page description';
$keywords = 'your, keywords, here';

// Start output buffering for content
ob_start();
?>

<!-- Your page content here -->
<div class="container">
    <h1>Welcome to JobSpace</h1>
    <p>Your content goes here...</p>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../layouts/public-layout.php';
?>
```

2. **Direct Include Method**:
```php
<?php include 'includes/header/public-header.php'; ?>

<!-- Your content -->
<main>
    <!-- Page content -->
</main>

<?php include 'includes/footer/public-footer.php'; ?>
```

### Customization Options

#### Header Customization
```php
// Set custom variables before including header
$title = 'Custom Page Title';
$description = 'Custom description for SEO';
$keywords = 'custom, keywords, seo';
```

#### Navigation Menu
Edit the navigation menu in `public-header.php`:
```html
<nav class="nav-menu" id="navMenu">
    <li class="nav-item"><a href="/">Home</a></li>
    <li class="nav-item"><a href="/jobs">Jobs</a></li>
    <li class="nav-item"><a href="/companies">Companies</a></li>
    <!-- Add more menu items -->
</nav>
```

#### Footer Links
Customize footer links in `public-footer.php`:
```html
<ul class="footer-links">
    <li><a href="/custom-link">Custom Link</a></li>
    <!-- Add more links -->
</ul>
```

## 🎨 Styling

### CSS Variables
The components use CSS custom properties for easy theming:

```css
:root {
    --primary-color: #2563eb;      /* Main brand color */
    --secondary-color: #64748b;    /* Secondary text */
    --accent-color: #f59e0b;       /* Accent highlights */
    --dark-color: #1e293b;         /* Dark text */
    --light-color: #f8fafc;        /* Light backgrounds */
    --border-color: #e2e8f0;       /* Borders */
}
```

### Dark Theme
The components automatically support dark theme:
```css
[data-theme="dark"] {
    --dark-color: #f8fafc;
    --light-color: #1e293b;
    --border-color: #334155;
}
```

## 📱 Responsive Breakpoints

- **Desktop**: 1200px and above
- **Tablet**: 768px - 1199px
- **Mobile**: Below 768px
- **Small Mobile**: Below 480px

## ⚡ JavaScript Features

### Theme Toggle
```javascript
function toggleTheme() {
    // Switches between light and dark themes
    // Saves preference to localStorage
}
```

### Language Toggle
```javascript
function toggleLanguage() {
    // Switches between English and Bengali
    // Updates document language attribute
}
```

### Search Functionality
```javascript
function performSearch(query, dropdown) {
    // Performs real-time search
    // Displays results in dropdown
}
```

### Mobile Menu
```javascript
function toggleMobileMenu() {
    // Shows/hides mobile navigation
    // Updates ARIA attributes for accessibility
}
```

## 🔧 Dependencies

### CSS Dependencies
- Bootstrap 5.3.2
- Font Awesome 6.4.0
- Google Fonts (Inter)

### JavaScript Dependencies
- Bootstrap 5.3.2 Bundle
- Custom main.js file

## 🌐 Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+
- Mobile browsers (iOS Safari, Chrome Mobile)

## ♿ Accessibility Features

- **ARIA Labels**: Proper labeling for screen readers
- **Keyboard Navigation**: Full keyboard support
- **Focus Management**: Visible focus indicators
- **Screen Reader Announcements**: Theme and language changes
- **Semantic HTML**: Proper heading hierarchy and landmarks
- **Color Contrast**: WCAG AA compliant colors

## 🔍 SEO Features

- **Meta Tags**: Title, description, keywords
- **Open Graph**: Social media sharing optimization
- **Structured Data**: Ready for schema markup
- **Semantic HTML**: Proper heading structure
- **Fast Loading**: Optimized CSS and JavaScript

## 📋 Customization Examples

### Adding New Navigation Item
```html
<li class="nav-item">
    <a href="/new-page">New Page</a>
</li>
```

### Adding Footer Section
```html
<div class="footer-section">
    <h3 class="footer-title">New Section</h3>
    <ul class="footer-links">
        <li><a href="/link1">Link 1</a></li>
        <li><a href="/link2">Link 2</a></li>
    </ul>
</div>
```

### Custom Search Handler
```javascript
function performSearch(query, dropdown) {
    // Your custom search logic
    fetch(`/api/search?q=${query}`)
        .then(response => response.json())
        .then(data => displaySearchResults(data, dropdown));
}
```

## 🚨 Important Notes

1. **File Paths**: Ensure all asset paths are correct for your server setup
2. **PHP Includes**: Adjust include paths based on your directory structure
3. **API Integration**: Replace mock search with real API endpoints
4. **Image Assets**: Add actual images to `/assets/images/` directory
5. **Favicon**: Add favicon files to `/assets/icons/` directory

## 🤝 Contributing

When modifying these components:
1. Maintain responsive design principles
2. Test on multiple devices and browsers
3. Ensure accessibility standards are met
4. Update this documentation for any changes
5. Test with both light and dark themes

## 📞 Support

For questions or issues with these components, please refer to the main project documentation or contact the development team.
