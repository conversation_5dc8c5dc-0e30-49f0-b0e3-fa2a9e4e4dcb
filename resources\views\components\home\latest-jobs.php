<?php
/**
 * Latest Earning Opportunities Section - QuizSpace
 * Showcase different quiz categories and earning potential
 */

// Dynamic earning opportunities data
$earningOpportunities = [
    'title' => 'Latest Earning Opportunities',
    'subtitle' => 'Discover new ways to earn money through engaging quizzes and activities',
    'opportunities' => [
        [
            'category' => 'General Knowledge',
            'title' => 'Bangladesh History Quiz',
            'description' => 'Test your knowledge about Bangladesh\'s rich history and culture',
            'earning_per_question' => 50,
            'bonus_coins' => 25,
            'difficulty' => 'Easy',
            'questions_count' => 10,
            'time_limit' => '15 min',
            'participants' => 1250,
            'icon' => 'fas fa-landmark',
            'color' => 'primary',
            'badge' => 'Popular'
        ],
        [
            'category' => 'Science & Technology',
            'title' => 'Tech Innovation Quiz',
            'description' => 'Explore the latest in technology and scientific discoveries',
            'earning_per_question' => 75,
            'bonus_coins' => 40,
            'difficulty' => 'Medium',
            'questions_count' => 15,
            'time_limit' => '20 min',
            'participants' => 890,
            'icon' => 'fas fa-microchip',
            'color' => 'info',
            'badge' => 'High Reward'
        ],
        [
            'category' => 'Sports & Entertainment',
            'title' => 'Cricket World Quiz',
            'description' => 'Show your cricket knowledge and earn while having fun',
            'earning_per_question' => 60,
            'bonus_coins' => 30,
            'difficulty' => 'Easy',
            'questions_count' => 12,
            'time_limit' => '18 min',
            'participants' => 2100,
            'icon' => 'fas fa-baseball-ball',
            'color' => 'success',
            'badge' => 'Trending'
        ],
        [
            'category' => 'Business & Finance',
            'title' => 'Financial Literacy Quiz',
            'description' => 'Learn about finance and economics while earning money',
            'earning_per_question' => 100,
            'bonus_coins' => 50,
            'difficulty' => 'Hard',
            'questions_count' => 20,
            'time_limit' => '30 min',
            'participants' => 650,
            'icon' => 'fas fa-chart-line',
            'color' => 'warning',
            'badge' => 'Premium'
        ],
        [
            'category' => 'Language & Literature',
            'title' => 'Bengali Literature Quiz',
            'description' => 'Dive into the world of Bengali literature and poetry',
            'earning_per_question' => 65,
            'bonus_coins' => 35,
            'difficulty' => 'Medium',
            'questions_count' => 14,
            'time_limit' => '22 min',
            'participants' => 780,
            'icon' => 'fas fa-book',
            'color' => 'danger',
            'badge' => 'Cultural'
        ],
        [
            'category' => 'Current Affairs',
            'title' => 'World News Quiz',
            'description' => 'Stay updated with current events and earn rewards',
            'earning_per_question' => 55,
            'bonus_coins' => 28,
            'difficulty' => 'Easy',
            'questions_count' => 8,
            'time_limit' => '12 min',
            'participants' => 1450,
            'icon' => 'fas fa-globe',
            'color' => 'secondary',
            'badge' => 'Daily'
        ]
    ],
    'special_events' => [
        [
            'title' => 'Weekend Mega Quiz',
            'description' => 'Special weekend event with 5x rewards',
            'reward_multiplier' => '5x',
            'start_time' => 'Saturday 8 PM',
            'duration' => '2 hours',
            'icon' => 'fas fa-star'
        ],
        [
            'title' => 'Monthly Championship',
            'description' => 'Compete for the monthly champion title',
            'prize_pool' => '৳50,000',
            'registration_deadline' => '25th of month',
            'participants_limit' => '1000',
            'icon' => 'fas fa-trophy'
        ]
    ]
];
?>

<!-- Latest Earning Opportunities Section -->
<section class="py-5 bg-white" id="earning-opportunities">
    <div class="container">
        <!-- Section Header -->
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <h2 class="display-5 fw-bold mb-3"><?= htmlspecialchars($earningOpportunities['title']) ?></h2>
                <p class="lead text-muted"><?= htmlspecialchars($earningOpportunities['subtitle']) ?></p>
                <div class="d-flex justify-content-center">
                    <div class="bg-primary" style="width: 60px; height: 4px; border-radius: 2px;"></div>
                </div>
            </div>
        </div>

        <!-- Opportunities Grid -->
        <div class="row g-4 mb-5">
            <?php foreach ($earningOpportunities['opportunities'] as $index => $opportunity): ?>
            <div class="col-lg-4 col-md-6">
                <div class="opportunity-card h-100 bg-white rounded-4 shadow-sm border position-relative overflow-hidden">
                    <!-- Badge -->
                    <?php if (!empty($opportunity['badge'])): ?>
                    <div class="position-absolute top-0 end-0 m-3 z-3">
                        <span class="badge bg-<?= $opportunity['color'] ?> px-2 py-1 rounded-pill">
                            <?= htmlspecialchars($opportunity['badge']) ?>
                        </span>
                    </div>
                    <?php endif; ?>

                    <!-- Card Header -->
                    <div class="card-header bg-<?= $opportunity['color'] ?> bg-opacity-10 border-0 p-4">
                        <div class="d-flex align-items-center mb-3">
                            <div class="opportunity-icon me-3">
                                <div class="d-inline-flex align-items-center justify-content-center rounded-circle bg-<?= $opportunity['color'] ?> text-white p-3">
                                    <i class="<?= $opportunity['icon'] ?>" style="font-size: 1.5rem;"></i>
                                </div>
                            </div>
                            <div>
                                <h6 class="text-<?= $opportunity['color'] ?> mb-1 fw-bold"><?= htmlspecialchars($opportunity['category']) ?></h6>
                                <span class="badge bg-<?= $opportunity['color'] ?> bg-opacity-20 text-<?= $opportunity['color'] ?> small">
                                    <?= htmlspecialchars($opportunity['difficulty']) ?>
                                </span>
                            </div>
                        </div>
                        <h5 class="fw-bold text-dark mb-2"><?= htmlspecialchars($opportunity['title']) ?></h5>
                        <p class="text-muted mb-0 small"><?= htmlspecialchars($opportunity['description']) ?></p>
                    </div>

                    <!-- Card Body -->
                    <div class="card-body p-4">
                        <!-- Earning Info -->
                        <div class="earning-info mb-4">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="earning-item">
                                        <h6 class="text-success fw-bold mb-1">৳<?= $opportunity['earning_per_question'] ?></h6>
                                        <small class="text-muted">Per Question</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="earning-item">
                                        <h6 class="text-warning fw-bold mb-1"><?= $opportunity['bonus_coins'] ?> <i class="fas fa-coins"></i></h6>
                                        <small class="text-muted">Bonus Coins</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Quiz Details -->
                        <div class="quiz-details mb-4">
                            <div class="row g-2">
                                <div class="col-4">
                                    <div class="detail-item text-center p-2 bg-light rounded">
                                        <i class="fas fa-question-circle text-primary mb-1"></i>
                                        <div class="small fw-bold"><?= $opportunity['questions_count'] ?></div>
                                        <div class="small text-muted">Questions</div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="detail-item text-center p-2 bg-light rounded">
                                        <i class="fas fa-clock text-warning mb-1"></i>
                                        <div class="small fw-bold"><?= $opportunity['time_limit'] ?></div>
                                        <div class="small text-muted">Duration</div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="detail-item text-center p-2 bg-light rounded">
                                        <i class="fas fa-users text-info mb-1"></i>
                                        <div class="small fw-bold"><?= number_format($opportunity['participants']) ?></div>
                                        <div class="small text-muted">Players</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Potential Earning -->
                        <div class="potential-earning bg-success bg-opacity-10 rounded-3 p-3 mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <small class="text-muted">Potential Earning:</small>
                                    <div class="fw-bold text-success">
                                        ৳<?= $opportunity['earning_per_question'] * $opportunity['questions_count'] ?> +
                                        <?= $opportunity['bonus_coins'] * $opportunity['questions_count'] ?> <i class="fas fa-coins text-warning"></i>
                                    </div>
                                </div>
                                <i class="fas fa-arrow-trend-up text-success fa-lg"></i>
                            </div>
                        </div>

                        <!-- Action Button -->
                        <div class="text-center">
                            <a href="/quiz/<?= strtolower(str_replace(' ', '-', $opportunity['title'])) ?>"
                               class="btn btn-<?= $opportunity['color'] ?> btn-sm w-100 fw-bold">
                                <i class="fas fa-play me-2"></i>Start Quiz
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- Special Events Section -->
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="special-events bg-gradient-primary text-white rounded-4 p-4">
                    <div class="text-center mb-4">
                        <h4 class="fw-bold mb-2">🎉 Special Events & Competitions</h4>
                        <p class="opacity-90 mb-0">Join exclusive events for bigger rewards and prizes</p>
                    </div>

                    <div class="row g-4">
                        <?php foreach ($earningOpportunities['special_events'] as $event): ?>
                        <div class="col-md-6">
                            <div class="event-card bg-white bg-opacity-10 rounded-3 p-4 h-100">
                                <div class="d-flex align-items-start mb-3">
                                    <div class="event-icon me-3">
                                        <i class="<?= $event['icon'] ?> text-warning fa-2x"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="fw-bold mb-2"><?= htmlspecialchars($event['title']) ?></h6>
                                        <p class="opacity-90 mb-3 small"><?= htmlspecialchars($event['description']) ?></p>

                                        <div class="event-details">
                                            <?php if (isset($event['reward_multiplier'])): ?>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span class="small">Reward Multiplier:</span>
                                                <span class="fw-bold text-warning"><?= $event['reward_multiplier'] ?></span>
                                            </div>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span class="small">Start Time:</span>
                                                <span class="fw-bold"><?= $event['start_time'] ?></span>
                                            </div>
                                            <div class="d-flex justify-content-between">
                                                <span class="small">Duration:</span>
                                                <span class="fw-bold"><?= $event['duration'] ?></span>
                                            </div>
                                            <?php endif; ?>

                                            <?php if (isset($event['prize_pool'])): ?>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span class="small">Prize Pool:</span>
                                                <span class="fw-bold text-warning"><?= $event['prize_pool'] ?></span>
                                            </div>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span class="small">Registration Ends:</span>
                                                <span class="fw-bold"><?= $event['registration_deadline'] ?></span>
                                            </div>
                                            <div class="d-flex justify-content-between">
                                                <span class="small">Max Participants:</span>
                                                <span class="fw-bold"><?= $event['participants_limit'] ?></span>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="text-center">
                                    <a href="/events/<?= strtolower(str_replace(' ', '-', $event['title'])) ?>"
                                       class="btn btn-warning btn-sm fw-bold text-dark">
                                        <i class="fas fa-calendar-plus me-2"></i>Join Event
                                    </a>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bottom CTA -->
        <div class="row justify-content-center mt-5">
            <div class="col-lg-8 text-center">
                <div class="cta-section bg-light rounded-4 p-5">
                    <h4 class="fw-bold mb-3">Ready to Start Earning?</h4>
                    <p class="text-muted mb-4">Join thousands of users who are already earning money through fun quizzes</p>
                    <div class="d-flex justify-content-center gap-3 flex-wrap">
                        <a href="/register" class="btn btn-primary btn-lg px-4">
                            <i class="fas fa-user-plus me-2"></i>Sign Up Free
                        </a>
                        <a href="/quiz/demo" class="btn btn-outline-primary btn-lg px-4">
                            <i class="fas fa-play me-2"></i>Try Demo Quiz
                        </a>
                    </div>

                    <!-- Quick Stats -->
                    <div class="row mt-4">
                        <div class="col-md-4">
                            <div class="quick-stat">
                                <h6 class="text-success fw-bold">৳50-100</h6>
                                <small class="text-muted">Average per quiz</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="quick-stat">
                                <h6 class="text-primary fw-bold">5-30 min</h6>
                                <small class="text-muted">Time per quiz</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="quick-stat">
                                <h6 class="text-warning fw-bold">24/7</h6>
                                <small class="text-muted">Available anytime</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Earning Opportunities Styles -->
<style>
.opportunity-card {
    transition: all 0.3s ease;
    border: 1px solid rgba(0,0,0,0.08) !important;
}

.opportunity-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15) !important;
}

.opportunity-icon {
    transition: transform 0.3s ease;
}

.opportunity-card:hover .opportunity-icon {
    transform: scale(1.1) rotate(5deg);
}

.earning-item {
    transition: all 0.3s ease;
}

.opportunity-card:hover .earning-item {
    transform: scale(1.05);
}

.detail-item {
    transition: all 0.3s ease;
}

.detail-item:hover {
    background-color: #e9ecef !important;
    transform: translateY(-2px);
}

.potential-earning {
    transition: all 0.3s ease;
    border: 1px solid rgba(25, 135, 84, 0.2);
}

.opportunity-card:hover .potential-earning {
    background-color: rgba(25, 135, 84, 0.15) !important;
    transform: scale(1.02);
}

.special-events {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.event-card {
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.event-card:hover {
    background-color: rgba(255,255,255,0.2) !important;
    transform: translateY(-5px);
}

.event-icon {
    transition: transform 0.3s ease;
}

.event-card:hover .event-icon {
    transform: scale(1.2) rotate(10deg);
}

.cta-section {
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.cta-section:hover {
    border-color: #007bff;
    background-color: #f8f9fa !important;
}

.quick-stat {
    transition: transform 0.3s ease;
}

.quick-stat:hover {
    transform: scale(1.1);
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

/* Animation for cards */
.opportunity-card {
    animation: fadeInUp 0.6s ease forwards;
    opacity: 0;
    transform: translateY(30px);
}

<?php foreach ($earningOpportunities['opportunities'] as $index => $opportunity): ?>
.opportunity-card:nth-child(<?= $index + 1 ?>) {
    animation-delay: <?= $index * 0.1 ?>s;
}
<?php endforeach; ?>

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .opportunity-card {
        margin-bottom: 1.5rem;
    }

    .earning-info .row {
        text-align: center;
    }

    .quiz-details .row {
        gap: 0.5rem;
    }

    .event-card {
        margin-bottom: 1rem;
    }

    .cta-section {
        padding: 2rem !important;
    }

    .quick-stat {
        margin-bottom: 1rem;
    }
}

@media (max-width: 576px) {
    .opportunity-card .card-header {
        padding: 1.5rem !important;
    }

    .opportunity-card .card-body {
        padding: 1.5rem !important;
    }

    .detail-item {
        padding: 0.75rem !important;
    }

    .potential-earning {
        padding: 1rem !important;
    }
}

/* Loading animation for buttons */
.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* Pulse animation for badges */
.badge {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Hover effect for earning amounts */
.text-success, .text-warning {
    transition: all 0.3s ease;
}

.earning-item:hover .text-success {
    color: #198754 !important;
    text-shadow: 0 0 10px rgba(25, 135, 84, 0.3);
}

.earning-item:hover .text-warning {
    color: #ffc107 !important;
    text-shadow: 0 0 10px rgba(255, 193, 7, 0.3);
}
</style>