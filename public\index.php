<?php
/**
 * JobSpace - Main Entry Point
 * Simple routing for the application
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Get the request URI
$request = $_SERVER['REQUEST_URI'] ?? '/';
$path = parse_url($request, PHP_URL_PATH);

// Remove leading slash and clean path
$path = trim($path, '/');

// Default to home if empty
if (empty($path) || $path === 'index.php') {
    $path = 'home';
}

// Simple routing
switch ($path) {
    case 'home':
    case '':
        // Include home page
        include '../resources/views/pages/home.php';
        break;

    case 'about':
        include '../resources/views/pages/about.php';
        break;

    case 'contact':
        include '../resources/views/pages/contact.php';
        break;

    case 'auth/login':
        include '../resources/views/auth/login.php';
        break;

    case 'auth/register':
        include '../resources/views/auth/register.php';
        break;

    default:
        // 404 page
        http_response_code(404);
        include '../resources/views/errors/404.php';
        break;
}
?>