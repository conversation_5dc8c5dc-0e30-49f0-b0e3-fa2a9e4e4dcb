<?php
/**
 * JobSpace - Home Page
 * Professional quiz earning platform homepage
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// SEO Meta Information
$title = 'JobSpace - Earn Money Through Fun Quizzes | Quiz Earning Platform Bangladesh';
$description = 'Join JobSpace and earn real money by playing fun quizzes! Dual earning system with coins and cash. Start earning today - completely free to join.';
$keywords = 'quiz earning, make money online, earn money Bangladesh, online quiz, cash rewards, coin system, JobSpace';

// Open Graph Meta Tags
$og_title = 'JobSpace - Leading Job Portal in Bangladesh';
$og_description = 'Find your dream job from thousands of opportunities. Join JobSpace today and connect with top employers across Bangladesh.';
$og_image = '/assets/images/jobspace-og-image.jpg';
$og_url = '/';

// Schema.org data will be embedded directly in HTML

// Start output buffering for main content
ob_start();
?>

<!-- Schema.org Structured Data -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "JobSpace",
    "description": "<?= htmlspecialchars($description) ?>",
    "url": "/",
    "potentialAction": {
        "@type": "SearchAction",
        "target": "/jobs?q={search_term_string}",
        "query-input": "required name=search_term_string"
    },
    "publisher": {
        "@type": "Organization",
        "name": "JobSpace",
        "logo": {
            "@type": "ImageObject",
            "url": "/assets/images/logo.png"
        }
    }
}
</script>

<!-- Hero Section -->
<?php include __DIR__ . '/../components/home/<USER>'; ?>

<!-- Statistics Section -->
<?php include __DIR__ . '/../components/home/<USER>'; ?>

<!-- Features Section -->
<?php include __DIR__ . '/../components/home/<USER>'; ?>

<!-- Latest Jobs Section -->
<?php include __DIR__ . '/../components/home/<USER>'; ?>

<!-- How It Works Section -->
<?php include __DIR__ . '/../components/home/<USER>'; ?>

<!-- Services Section -->
<?php include __DIR__ . '/../components/home/<USER>'; ?>

<!-- Testimonials Section -->
<?php include __DIR__ . '/../components/home/<USER>'; ?>

<!-- Partners Section -->
<?php include __DIR__ . '/../components/home/<USER>'; ?>

<!-- FAQ Section -->
<?php include __DIR__ . '/../components/home/<USER>'; ?>

<!-- Newsletter Section -->
<?php include __DIR__ . '/../components/home/<USER>'; ?>

<!-- Call to Action Section -->
<?php include __DIR__ . '/../components/home/<USER>'; ?>

<?php
// Get the content and include the layout
$content = ob_get_clean();

// Try to include layout, fallback if not found
if (file_exists(__DIR__ . '/../layouts/public-layout.php')) {
    include __DIR__ . '/../layouts/public-layout.php';
} else {
    // Fallback HTML if layout not found
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title><?= htmlspecialchars($title) ?></title>
        <meta name="description" content="<?= htmlspecialchars($description) ?>">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <!-- Simple Header -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand fw-bold" href="/">
                    <i class="fas fa-briefcase me-2"></i>JobSpace
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="/auth/login"><i class="fas fa-sign-in-alt me-1"></i>Login</a>
                    <a class="nav-link" href="/auth/register"><i class="fas fa-user-plus me-1"></i>Register</a>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main>
            <?= $content ?>
        </main>

        <!-- Simple Footer -->
        <footer class="bg-dark text-white py-4 mt-5">
            <div class="container text-center">
                <p>&copy; <?= date('Y') ?> JobSpace. All rights reserved.</p>
            </div>
        </footer>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    <?php
}
?>