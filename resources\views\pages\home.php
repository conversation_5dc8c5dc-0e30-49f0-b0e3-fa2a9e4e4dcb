<?php
// Set page-specific variables
$title = 'Home - JobSpace | Find Your Dream Job';
$description = 'Discover thousands of job opportunities from top companies. Start your career journey with JobSpace today.';
$keywords = 'jobs, career, employment, job search, hiring, recruitment';

// Start output buffering for content
ob_start();
?>

<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center min-vh-100">
            <div class="col-lg-6">
                <h1 class="hero-title">Find Your <span class="text-primary">Dream Job</span> Today</h1>
                <p class="hero-description">Connect with top employers and discover opportunities that match your skills and aspirations.</p>

                <div class="hero-search">
                    <form class="search-form">
                        <div class="row g-2">
                            <div class="col-md-4">
                                <input type="text" class="form-control" placeholder="Job title or keyword">
                            </div>
                            <div class="col-md-4">
                                <select class="form-select">
                                    <option>Select Location</option>
                                    <option>Dhaka</option>
                                    <option>Chittagong</option>
                                    <option>Sylhet</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search"></i> Search Jobs
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <div class="hero-stats">
                    <div class="row">
                        <div class="col-4">
                            <div class="stat-item">
                                <h3>10K+</h3>
                                <p>Active Jobs</p>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stat-item">
                                <h3>5K+</h3>
                                <p>Companies</p>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stat-item">
                                <h3>50K+</h3>
                                <p>Job Seekers</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="hero-image">
                    <img src="/assets/images/hero-illustration.svg" alt="Job Search Illustration" class="img-fluid">
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Jobs Section -->
<section class="featured-jobs py-5">
    <div class="container">
        <div class="section-header text-center mb-5">
            <h2>Featured Jobs</h2>
            <p>Discover the latest job opportunities from top companies</p>
        </div>

        <div class="row">
            <!-- Job cards would be dynamically generated here -->
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="job-card">
                    <div class="job-header">
                        <img src="/assets/images/company-logo.png" alt="Company" class="company-logo">
                        <div class="job-meta">
                            <h5>Senior Software Developer</h5>
                            <p>Tech Solutions Ltd.</p>
                        </div>
                    </div>
                    <div class="job-details">
                        <span class="badge bg-primary">Full-time</span>
                        <span class="badge bg-secondary">Remote</span>
                        <p class="salary">৳80,000 - ৳120,000</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.hero-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 2rem 0;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero-description {
    font-size: 1.2rem;
    color: #64748b;
    margin-bottom: 2rem;
}

.hero-search {
    margin-bottom: 3rem;
}

.search-form .form-control,
.search-form .form-select {
    height: 50px;
    border-radius: 8px;
}

.hero-stats .stat-item {
    text-align: center;
}

.hero-stats h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #2563eb;
    margin-bottom: 0.5rem;
}

.hero-stats p {
    color: #64748b;
    margin: 0;
}

.job-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    transition: transform 0.3s ease;
}

.job-card:hover {
    transform: translateY(-4px);
}

.job-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.company-logo {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    margin-right: 1rem;
}

.job-meta h5 {
    margin: 0 0 0.25rem 0;
    font-weight: 600;
}

.job-meta p {
    margin: 0;
    color: #64748b;
}

.job-details .badge {
    margin-right: 0.5rem;
}

.salary {
    font-weight: 600;
    color: #059669;
    margin: 0.5rem 0 0 0;
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-description {
        font-size: 1.1rem;
    }
}
</style>

<?php
$content = ob_get_clean();
include __DIR__ . '/../layouts/public-layout.php';
?>